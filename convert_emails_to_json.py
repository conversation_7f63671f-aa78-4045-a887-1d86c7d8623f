#!/usr/bin/env python3
"""
脚本：将 microsoft_emails_will_use.txt 中的账号密码转换为 JSON 数组格式
"""

import json
import os

def convert_emails_to_json():
    """
    读取 microsoft_emails_will_use.txt 文件，将账号密码转换为 JSON 数组格式
    """
    input_file = "data/microsoft_emails_will_use.txt"
    output_file = "data/microsoft_emails.json"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    accounts = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                
                # 分割邮箱和密码
                if ':' in line:
                    email, password = line.split(':', 1)  # 使用 split(':', 1) 防止密码中包含冒号
                    accounts.append({
                        "email": email.strip(),
                        "password": password.strip()
                    })
                else:
                    print(f"警告：第 {line_num} 行格式不正确，跳过: {line}")
        
        # 将结果写入 JSON 文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)
        
        print(f"转换完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"共转换了 {len(accounts)} 个账号")
        
        # 显示前几个账号作为示例
        if accounts:
            print("\n前3个账号示例:")
            for i, account in enumerate(accounts[:3], 1):
                print(f"{i}. {account}")
    
    except Exception as e:
        print(f"转换过程中出现错误: {e}")

if __name__ == "__main__":
    convert_emails_to_json()
